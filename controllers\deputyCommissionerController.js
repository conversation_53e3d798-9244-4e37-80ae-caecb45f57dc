const ImportDutyWaiverApplicationModel = require('../models/importDutyWaiverApplication').ImportDutyWaiverApplicationModel;
const StampDutyApplicationModel = require('../models/stampDutyExemptionApplication').StampDutyApplicationModel;
const MailController = require('../controllers/mailController');
const MailFormatter = require('../controllers/mailFormatController');
const { islands, calendar } = require('../constants');
const httpConstants = require('http2').constants;
const statusCardHolderHelper = require('../shared/statusCardHolder.helper');

exports.getDashboard = async function (req, res) {
  try {
    let filters = {
      "showPendingActions": !!req.body.showPendingActions,
    };

    let statusQuery = ['PENDING DEPUTY COMMISSIONER', 'SAVED DEPUTY COMMISSIONER', 'CONFLICTED BY CUSTOMS OFFICER', 'CONFLICTED BY DEPUTY COMMISSIONER'];

    // Filter for pending actions only
    if (req.body.showPendingActions) {
      statusQuery = ['PENDING DEPUTY COMMISSIONER', 'SAVED DEPUTY COMMISSIONER', 'CONFLICTED BY CUSTOMS OFFICER', 'CONFLICTED BY DEPUTY COMMISSIONER'];
    }

    let importDutyWaiverApplications = [];
    importDutyWaiverApplications = await ImportDutyWaiverApplicationModel.find({ status: { $in: statusQuery } });

    // Helper function to determine if submission has pending actions
    const hasActionsPending = (submission) => {
      const actionableStatuses = ['PENDING DEPUTY COMMISSIONER', 'SAVED DEPUTY COMMISSIONER', 'CONFLICTED BY CUSTOMS OFFICER', 'CONFLICTED BY DEPUTY COMMISSIONER'];
      return actionableStatuses.includes(submission.status);
    };

    importDutyWaiverApplications.map((app) => {
      app.hasActionsPending = hasActionsPending(app);
    });

    res.render('deputy-commissioner/dashboard',
      {
        importDutyWaiverApplications,
        filters,
      });

  } catch (e) {
    console.log("error: ", e);
    res.redirect('/');
  }
};

exports.getImportDutyWaiverView = async function (req, res) {
  try {
    req.session.files = {};
    const application = await ImportDutyWaiverApplicationModel.findById(req.params.id);
    application.propertyDetails.parcelFirstPart = application.propertyDetails.parcel.split('/')[0];
    application.propertyDetails.parcelSecondPart = application.propertyDetails.parcel.split('/')[1];
    const filesInformation = {
      affidavit: { name: "Affidavit", files: application.affidavit, filesCount: application.affidavit.length, validated: application.deputyCommissionerTier ? application.deputyCommissionerTier.validations?.affidavit : false },
      marriageCertificates: { name: "Marriage Certificates", files: application.marriageCertificates, filesCount: application.marriageCertificates.length, validated: application.deputyCommissionerTier ? application.deputyCommissionerTier.validations?.marriageCertificates : false },
      birthCertificate: { name: "Copy of applicant s TCI birth certificate and a valid Government issued identification", files: application.birthCertificate, filesCount: application.birthCertificate.length, validated: application.deputyCommissionerTier ? application.deputyCommissionerTier.validations?.birthCertificate : false },
      parentsDocuments: { name: "Parent’s documents", files: application.parentsDocuments, filesCount: application.parentsDocuments.length, validated: application.deputyCommissionerTier ? application.deputyCommissionerTier.validations?.parentsDocuments : false },
      itemsInvoices: { name: "Copies of invoices", files: application.itemsInvoices, filesCount: application.itemsInvoices.length, validated: application.deputyCommissionerTier ? application.deputyCommissionerTier.validations?.itemsInvoices : false },
      buildingPictures: { name: "Building photographs", files: application.buildingPictures, filesCount: application.buildingPictures.length, validated: application.deputyCommissionerTier ? application.deputyCommissionerTier.validations?.buildingPictures : false },
      buildingPermit: { name: "Building permit", files: application.buildingPermit, filesCount: application.buildingPermit.length, validated: application.deputyCommissionerTier ? application.deputyCommissionerTier.validations?.buildingPermit : false },
      landRegister: { name: "A certified copy of the Land Register Extract", files: application.landRegister, filesCount: application.landRegister.length, validated: application.deputyCommissionerTier ? application.deputyCommissionerTier.validations?.landRegister : false },
      valuation: { name: "Valuation", files: application.valuation, filesCount: application.valuation.length, validated: application.deputyCommissionerTier ? application.deputyCommissionerTier.validations?.valuation : false },
      statusCard: { name: "Turks & Caicos Islander Status Card", files: application.statusCard, filesCount: application.statusCard.length, validated: application.deputyCommissionerTier ? application.deputyCommissionerTier.validations?.statusCard : false },
      botcCertificate: { name: "Copy of BOTC certificate along with a Government issued identification", files: application.botcCertificate, filesCount: application.botcCertificate.length, validated: application.deputyCommissionerTier ? application.deputyCommissionerTier.validations?.botcCertificate : false },
    }
    let applicants = [{
      firstName: application.firstName,
      lastName: application.lastName,
      dateOfBirth: application.dateOfBirth,
      statusCardNumber: application.statusCardNumber,
    }];

    if (application.applicants && application.applicants.length) {
      applicants.push(...application.applicants.map(ap => {
        return {
          firstName: ap.firstName,
          lastName: ap.lastName,
          dateOfBirth: ap.dateOfBirth,
        }
      }));
    }


    applicants = await Promise.all(applicants.map(async (ap) => {
      // Search in import duty waiver submissions
      const previousImportDutyApplications = await ImportDutyWaiverApplicationModel.find({
        '$or': [
          {
            'lastName': ap.lastName,
            'dateOfBirth': ap.dateOfBirth,
          },
          {
            'applicants.lastName': ap.lastName,
            'applicants.dateOfBirth': ap.dateOfBirth,
          },
          {
            'statusCardNumber': { '$exists': true, '$nin': ["", null], '$eq': ap.statusCardNumber },
          }
        ],
      });
      // Search in import duty waiver submissions
      const previousStampDutyExemptionApplications = await StampDutyApplicationModel.find({
        '$or': [
          {
            'lastName': ap.lastName,
            'dateOfBirth': ap.dateOfBirth,
          },
          {
            'applicants.lastName': ap.lastName,
            'applicants.dateOfBirth': ap.dateOfBirth,
          },
          {
            'statusCardNumber': { '$exists': true, '$nin': ["", null], '$eq': ap.statusCardNumber },
          }
        ],
      });
      ap.previousApplications = {
        importDuty: previousImportDutyApplications,
        stampDutyExemption: previousStampDutyExemptionApplications,
      }
      ap.totalValue =
        [
          previousImportDutyApplications.reduce((a, b) => a + b.remittedAmount || 0, 0),
          previousStampDutyExemptionApplications.reduce((a, b) => a + b.remittedAmount || 0, 0),
        ].reduce((a, b) => a + b, 0);
      return ap;
    }));

    const { foundCardHolder, statusInformationColor } = await statusCardHolderHelper.getCardHolderStatus(application);

    res.render('deputy-commissioner/open-import-duty-waiver-form',
      {
        application: application,
        validations: application.deputyCommissionerTier ? application.deputyCommissionerTier.validations : null,
        islands,
        calendar,
        applicants,
        filesInformation,
        foundCardHolder,
        statusInformationColor
      });

  } catch (e) {
    console.log(e);
    return res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).end();
  }
};

exports.updateImportDutyWaiver = async function (req, res) {
  try {
    const validation = {
      applicantDetails: req.body.validatedApplicantDetails === 'Yes',
      personalDetails: req.body.validatedPersonalDetails === 'Yes',
      additionalApplicants: req.body.validatedAdditionalApplicants === 'Yes',
      propertyDetails: req.body.validatedPropertyDetails === 'Yes',
      affidavit: req.body.validated_affidavit === 'Yes',
      marriageCertificates: req.body.validated_marriageCertificates === 'Yes',
      birthCertificate: req.body.validated_birthCertificate === 'Yes',
      parentsDocuments: req.body.validated_parentsDocuments === 'Yes',
      itemsInvoices: req.body.validated_itemsInvoices === 'Yes',
      buildingPictures: req.body.validated_buildingPictures === 'Yes',
      buildingPermit: req.body.validated_buildingPermit === 'Yes',
      landRegister: req.body.validated_landRegister === 'Yes',
      valuation: req.body.validated_valuation === 'Yes',
      statusCard: req.body.validated_statusCard === 'Yes',
      botcCertificate: req.body.validated_botcCertificate === 'Yes',
    }

    await ImportDutyWaiverApplicationModel.updateOne({ _id: req.params.id }, {
      'deputyCommissionerTier.validations': validation,
      'deputyCommissionerTier.username': req.session.user.username,
      status: 'SAVED DEPUTY COMMISSIONER',
      remittedAmount: req.body.remittedAmount ? req.body.remittedAmount : 0,
      valueOfGoods: req.body.valueOfGoods ? req.body.valueOfGoods : 0
    });


    return res.status(httpConstants.HTTP_STATUS_OK).json({ "success": true });

  } catch (e) {
    console.log("error: ", e);
    res.redirect('/deputy-commissioner/dashboard');
  }
};

exports.approveImportDutyWaiver = async function (req, res) {
  try {
    const validation = {
      applicantDetails: req.body.validatedApplicantDetails === 'Yes',
      personalDetails: req.body.validatedPersonalDetails === 'Yes',
      additionalApplicants: req.body.validatedAdditionalApplicants === 'Yes',
      propertyDetails: req.body.validatedPropertyDetails === 'Yes',
      affidavit: req.body.validated_affidavit === 'Yes',
      marriageCertificates: req.body.validated_marriageCertificates === 'Yes',
      birthCertificate: req.body.validated_birthCertificate === 'Yes',
      parentsDocuments: req.body.validated_parentsDocuments === 'Yes',
      itemsInvoices: req.body.validated_itemsInvoices === 'Yes',
      buildingPictures: req.body.validated_buildingPictures === 'Yes',
      buildingPermit: req.body.validated_buildingPermit === 'Yes',
      landRegister: req.body.validated_landRegister === 'Yes',
      valuation: req.body.validated_valuation === 'Yes',
      statusCard: req.body.validated_statusCard === 'Yes',
      botcCertificate: req.body.validated_botcCertificate === 'Yes',
    }

    const submission = await ImportDutyWaiverApplicationModel.findOne({ _id: req.params.id });
    const comment = {
      date: new Date(),
      internalComments: req.body.internalComments,
      user: req.session.user.username,
      status: 'APPROVED',
    };
    if (submission.deputyCommissionerTier && submission.deputyCommissionerTier.comments && submission.deputyCommissionerTier.comments.length) {
      submission.deputyCommissionerTier.comments.push(comment);
    } else {
      submission.deputyCommissionerTier = {
        comments: [comment],
      }
    }
    const updatedSubmission = await ImportDutyWaiverApplicationModel.findOneAndUpdate({ _id: req.params.id }, {
      'deputyCommissionerTier.validations': validation,
      'deputyCommissionerTier.approvedAt': new Date(),
      'deputyCommissionerTier.username': req.session.user.username,
      'deputyCommissionerTier.comments': submission.deputyCommissionerTier.comments,
      status: 'APPROVED',
      remittedAmount: req.body.remittedAmount ? req.body.remittedAmount : 0,
      valueOfGoods: req.body.valueOfGoods ? req.body.valueOfGoods : 0
    }, { new: true });

    const email = updatedSubmission.filingYourself ? updatedSubmission.email : updatedSubmission.personalInformation?.email;
    let emailContent = await MailFormatter.generateEmailImportDutyWaiverApprove(updatedSubmission);
    await MailController.asyncSend([email, process.env.EMAIL_LAND_OFFICER_RECIPIENT],
      'Import Duty Waiver Application approved',
      emailContent.textString,
      emailContent.htmlString,
      emailContent.pdf,
      'Official Letter of Approval - Application for Home Owner Policy.pdf'
    );

    return res.status(httpConstants.HTTP_STATUS_OK).json({ "success": true });

  } catch (e) {
    console.log("error: ", e);
    res.redirect('/deputy-commissioner/dashboard');
  }
};

exports.declineImportDutyWaiver = async function (req, res) {
  try {
    const submission = await ImportDutyWaiverApplicationModel.findOne({ _id: req.params.id });
    const comment = {
      date: new Date(),
      internalComments: req.body.internalComments,
      declineReason: req.body.declineReason,
      user: req.session.user.username,
      status: 'DECLINED',
    };
    if (submission.deputyCommissionerTier && submission.deputyCommissionerTier.comments && submission.deputyCommissionerTier.comments.length) {
      submission.deputyCommissionerTier.comments.push(comment);
    } else {
      submission.deputyCommissionerTier = {
        comments: [comment],
      }
    }

    const defaultValidations = {
      applicantDetails: false,
      personalDetails: false,
      additionalApplicants: false,
      propertyDetails: false,
      affidavit: false,
      marriageCertificates: false,
      birthCertificate: false,
      parentsDocuments: false,
      itemsInvoices: false,
      buildingPictures: false,
      buildingPermit: false,
      landRegister: false,
      valuation: false,
      statusCard: false,
      botcCertificate: false,
    }

    await ImportDutyWaiverApplicationModel.updateOne({ _id: req.params.id }, {
      'deputyCommissionerTier.declinedAt': new Date(),
      'deputyCommissionerTier.username': req.session.user.username,
      'customsOfficerTier.validations': defaultValidations,
      'deputyCommissionerTier.validations': defaultValidations,
      'deputyCommissionerTier.comments': submission.deputyCommissionerTier.comments,
      status: 'DECLINED BY DEPUTY COMMISSIONER',
    });

    return res.status(httpConstants.HTTP_STATUS_OK).json({ "success": true });

  } catch (e) {
    console.log("error: ", e);
    res.redirect('/deputy-commissioner/dashboard');
  }
};

exports.conflictImportDutyWaiver = async function (req, res) {
  try {
    const submission = await ImportDutyWaiverApplicationModel.findOne({ _id: req.params.id });
    const comment = {
      date: new Date(),
      internalComments: req.body.internalComments,
      user: req.session.user.username,
      status: 'CONFLICTED',
    };
    if (submission.deputyCommissionerTier && submission.deputyCommissionerTier.comments && submission.deputyCommissionerTier.comments.length) {
      submission.deputyCommissionerTier.comments.push(comment);
    } else {
      submission.deputyCommissionerTier = {
        comments: [comment],
      }
    }
    await ImportDutyWaiverApplicationModel.updateOne({ _id: req.params.id }, {
      'deputyCommissionerTier.conflictedAt': new Date(),
      'deputyCommissionerTier.username': req.session.user.username,
      'deputyCommissionerTier.comments': submission.deputyCommissionerTier.comments,
      status: 'CONFLICTED BY DEPUTY COMMISSIONER',
    });


    return res.status(httpConstants.HTTP_STATUS_OK).json({ "success": true });

  } catch (e) {
    console.log("error: ", e);
    res.redirect('/deputy-commissioner/dashboard');
  }
};

exports.requestInformationImportDutyWaiver = async function (req, res) {
  try {

    const validation = {
      applicantDetails: req.body.validatedApplicantDetails === 'Yes',
      personalDetails: req.body.validatedPersonalDetails === 'Yes',
      additionalApplicants: req.body.validatedAdditionalApplicants === 'Yes',
      propertyDetails: req.body.validatedPropertyDetails === 'Yes',
      affidavit: req.body.validated_affidavit === 'Yes',
      marriageCertificates: req.body.validated_marriageCertificates === 'Yes',
      birthCertificate: req.body.validated_birthCertificate === 'Yes',
      parentsDocuments: req.body.validated_parentsDocuments === 'Yes',
      itemsInvoices: req.body.validated_itemsInvoices === 'Yes',
      buildingPictures: req.body.validated_buildingPictures === 'Yes',
      buildingPermit: req.body.validated_buildingPermit === 'Yes',
      landRegister: req.body.validated_landRegister === 'Yes',
      valuation: req.body.validated_valuation === 'Yes',
      statusCard: req.body.validated_statusCard === 'Yes',
      botcCertificate: req.body.validated_botcCertificate === 'Yes',
    }

    await ImportDutyWaiverApplicationModel.updateOne({ _id: req.params.id }, {
      'deputyCommissionerTier.validations': validation,
      'deputyCommissionerTier.username': req.session.user.username,
      status: 'SAVED DEPUTY COMMISSIONER',
      remittedAmount: req.body.remittedAmount ? req.body.remittedAmount : 0,
      valueOfGoods: req.body.valueOfGoods ? req.body.valueOfGoods : 0
    });


    const submission = await ImportDutyWaiverApplicationModel.findOne({ _id: req.params.id });
    const newRequest = {
      requests: req.body.requestedInformation,
      createdBy: req.session.user.username,
      createdAt: new Date(),
      internalComments: req.body.internalComments,
      managementComments: req.body.managementComments,
    };

    if (submission.informationRequests && submission.informationRequests.length) {
      submission.informationRequests.push(newRequest);
    } else {
      submission.informationRequests = [newRequest];
    }
    await ImportDutyWaiverApplicationModel.updateOne({ _id: req.params.id }, {
      informationRequests: submission.informationRequests,
    });

    const email = submission.filingYourself ? submission.email : submission.personalInformation?.email;
    let emailContent = MailFormatter.generateEmailImportDutyWaiverInformationRequest(submission);
    await MailController.asyncSend([email],
      'Import Duty Waiver Information Requested',
      emailContent.textString,
      emailContent.htmlString
    );

    return res.status(httpConstants.HTTP_STATUS_OK).json({ "success": true });

  } catch (e) {
    console.log("error: ", e);
    res.redirect('/deputy-commissioner/dashboard');
  }
};
