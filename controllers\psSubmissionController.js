const ExemptionModel = require('../models/exemptions');
const uploadController = require('../controllers/uploadController');
const StampDutyApplicationModel = require('../models/stampDutyExemptionApplication').StampDutyApplicationModel;
const ImportDutyWaiverApplicationModel = require('../models/importDutyWaiverApplication').ImportDutyWaiverApplicationModel;
const ReductionApplicationModel = require('../models/stampDutyReductionApplication').ReductionApplicationModel;
const { v4: uuidv4 } = require('uuid');
const MailController = require('../controllers/mailController');
const MailFormatter = require('../controllers/mailFormatController');
const { islands, calendar } = require('../constants');
const httpConstants = require('http2').constants;
const statusCardHolderHelper = require('../shared/statusCardHolder.helper');

exports.getDashboard = async function (req, res) {
  try {
    let query = [{}];
    let stampDutyApplicationQuery = [{}];
    let importDutyApplicationQuery = [{}];
    let assignedQuery = [];
    let status = [];
    let filters = {
      "searchFilter": req.body.searchFilter,
      "exemptionType": req.body.exemptionType,
      "searchDeclined": !!req.body.isDeclined,
      "searchCompleted": !!req.body.searchCompleted,
      "showPendingActions": !!req.body.showPendingActions,
    };

    if (req.body.isDeclined) {
      status.push('DECLINED')
    }

    if (req.body.searchCompleted) {
      status.push('COMPLETED')
    }

    if (!req.body.isDeclined && !req.body.searchCompleted) {
      status.push('PENDING PS', 'TRANSFER COMPLETED', 'CONFLICT')
    }

    // Filter for pending actions only
    if (req.body.showPendingActions) {
      status = ['PENDING PS', 'CONFLICTED BY STAMP DUTY OFFICER', 'SAVED PS OFFICER'];
    }

    query.push({ "status": { $in: status } });
    assignedQuery.push({ "status": 'APPROVED PS' });
    assignedQuery.push({ "psOfficer.email": req.session.user.username });

    let stampDutyActionableStatuses = ["PENDING PS", "CONFLICTED BY STAMP DUTY OFFICER", "SAVED PS OFFICER"];
    stampDutyApplicationQuery.push({ "status": { $in: stampDutyActionableStatuses } });
    if (req.body.searchFilter && req.body.searchFilter.length > 2) {
      const searchText = {
        $or: [{ 'transferorName': { $regex: req.body.searchFilter, $options: 'i' } },
        { 'transfereeName': { $regex: req.body.searchFilter, $options: 'i' } },
        { 'parcelNumber': { $regex: req.body.searchFilter, $options: 'i' } },
        { 'parcelTextNumber': { $regex: req.body.searchFilter, $options: 'i' } }]
      };
      assignedQuery.push(searchText);
      query.push(searchText);
      const searchText2 = {
        $or: [
          { 'firstName': { $regex: req.body.searchFilter, $options: 'i' } },
          { 'lastName': { $regex: req.body.searchFilter, $options: 'i' } },
          { 'applicantDetails.firstName': { $regex: req.body.searchFilter, $options: 'i' } },
          { 'applicantDetails.lastName': { $regex: req.body.searchFilter, $options: 'i' } },
          { 'companyDetails.name': { $regex: req.body.searchFilter, $options: 'i' } },
          { 'propertyDetails.parcel': { $regex: req.body.searchFilter, $options: 'i' } }
        ]
      };
      stampDutyApplicationQuery.push(searchText2);
      importDutyApplicationQuery.push(searchText2);
    }
    let exceptionType = '';
    if (req.body.exemptionType === 'love-and-affection') {
      exceptionType = 'Natural Love & Affection';
    } else if (req.body.exemptionType === 'section-exemptions') {
      exceptionType = 'Section 23 & 28 Exemptions';
    } else if (req.body.exemptionType === 'charitable-institution') {
      exceptionType = 'Transfers to Charitable Institutions';
    }
    else if (req.body.exemptionType === 'transmission') {
      exceptionType = 'Transmission';
    }
    else if (req.body.exemptionType === 'refunds') {
      exceptionType = 'Refunds'
    }
    else if (req.body.exemptionType === 'remissions') {
      exceptionType = 'Remissions'
    }

    let landOfficers = [];
    let assignedByPs = [];
    let stampDutyApplications = [];
    let reductionApplications = [];
    let importDutyApplications = [];

    if (exceptionType) {
      query.push({ "exemptionType": exceptionType });
      assignedQuery.push({ "exemptionType": exceptionType });
    }

    if (!filters.exemptionType || exceptionType || filters.exemptionType === 'all') {
      landOfficers = await ExemptionModel.find({ $and: query });
      assignedByPs = await ExemptionModel.find({ $and: assignedQuery });
    }


    if (!filters.exemptionType || filters.exemptionType === 'exemption-application' || filters.exemptionType === 'all') {
      stampDutyApplications = await StampDutyApplicationModel.find({ $and: stampDutyApplicationQuery }).limit(100);
    }

    if (!filters.exemptionType || filters.exemptionType === 'Stamp Duty Reduction' || filters.exemptionType === 'all') {
      reductionApplications = await ReductionApplicationModel.find({ $and: stampDutyApplicationQuery }).limit(100);
    }

    if (!filters.exemptionType || filters.exemptionType === 'Import Duty Waiver' || filters.exemptionType === 'all') {
      importDutyApplications = await ImportDutyWaiverApplicationModel.find({ $and: importDutyApplicationQuery }).limit(100);
    }

    // Helper function to determine if submission has pending actions
    const hasActionsPending = (submission, type) => {
      // For PS Officer, actions are available for these statuses
      const actionableStatuses = ['PENDING PS', 'CONFLICTED BY STAMP DUTY OFFICER', 'SAVED PS OFFICER'];
      return actionableStatuses.includes(submission.status);
    };

    landOfficers.map((updateStatus) => {
      if (updateStatus.status === "SEND TO EXCHANGE OF INFORMATION UNIT") {
        updateStatus.status = "SUBMITTED";
      }
      updateStatus.hasActionsPending = hasActionsPending(updateStatus, 'exemption');
    });

    stampDutyApplications.map((app) => {
      app.hasActionsPending = hasActionsPending(app, 'stampDutyApplication');
    });

    reductionApplications.map((app) => {
      app.hasActionsPending = hasActionsPending(app, 'reductionApplication');
    });

    importDutyApplications.map((app) => {
      app.hasActionsPending = hasActionsPending(app, 'importDutyApplication');
    });

    if (assignedByPs) {

      landOfficers = [...landOfficers, ...assignedByPs.filter((submission) => submission.status !== 'APPROVED PS' &&
        submission.exemptionType !== 'Refunds')];
    }

    res.render('ps-submissions/ps-dashboard',
      {
        landOfficers,
        stampDutyApplications,
        reductionApplications,
        importDutyApplications,
        filters,
      });

  } catch (e) {
    console.log("error: ", e);
    res.redirect('/');
  }
};

exports.getSubmissionsView = async function (req, res) {
  try {
    req.session.files = {};
    const landOfficer = await ExemptionModel.findById(req.params.submissionId);

    const canComplete = landOfficer && (landOfficer.status === "APPROVED PS" || landOfficer.status === "TRANSFER COMPLETED");

    res.render('ps-submissions/open-ps-submission-form',
      {
        landOfficer: landOfficer,
        canComplete: canComplete
      });

  } catch (e) {
    console.log(e);
    return res.status(500).end();
  }
};

exports.uploadSignedDocument = async function (req, res, next) {
  try {
    const landOfficer = await ExemptionModel.findById(req.params.submissionId);

    if (!landOfficer) {
      console.log("error not found");
      const err = new Error('Submission not found');
      err.status = 404;
      return next(err);
    }

    let uploadedFiles = req.files['fileUploaded'];

    if (uploadedFiles && uploadedFiles.length > 0) {
      uploadedFiles = uploadedFiles.map((itemToUpload) => {
        let name = "";
        if (req.body.fileGroup === "signed-files") {
          name = landOfficer.signedFiles.internal.replace(/[\s’'/()]/g, '');
        }
        else if (req.body.fileGroup === "transfer-files") {
          name = landOfficer.transferCompletedFiles.internal.replace(/[\s’'/()]/g, '');
        }
        else if (req.body.fileGroup === "company-information-files") {
          name = landOfficer.companyInformationFiles.internal.replace(/[\s’'/()]/g, '');
        }
        else if (req.body.fileGroup === "stamp-duty-additional-information-files") {
          name = landOfficer.stampDutyAdditionalFiles.internal.replace(/[\s’'/()]/g, '');
        }

        uploadController.moveUpload(itemToUpload, name, landOfficer._id.toString());
        return {
          fileId: uuidv4(),
          fileTypeId: req.body.fileTypeId,
          fieldName: itemToUpload.fieldname.replace(/fileUploaded/i, req.body.fileName),
          originalName: itemToUpload.originalname,
          encoding: itemToUpload.encoding,
          mimeType: itemToUpload.mimetype,
          blobName: itemToUpload.blobName.replace(/fileUploaded/i, req.body.fileName),
          container: itemToUpload.container,
          blob: itemToUpload.blob.replace(/fileUploaded/i, req.body.fileName),
          blobType: itemToUpload.blobType,
          size: itemToUpload.size,
          etag: itemToUpload.etag,
          url: itemToUpload.url.replace(/fileUploaded/i, landOfficer._id.toString() + "/" + req.body.fileName),
        };
      });

      if (req.body.fileGroup === "signed-files") {
        landOfficer.markModified("signedFiles");
        landOfficer.signedFiles.uploadFiles = [...landOfficer.signedFiles.uploadFiles, ...uploadedFiles];
      }
      else if (req.body.fileGroup === "transfer-files") {
        landOfficer.markModified("transferCompletedFiles");
        landOfficer.transferCompletedFiles.uploadFiles = [...landOfficer.transferCompletedFiles.uploadFiles, ...uploadedFiles];
      }
      else if (req.body.fileGroup === "company-information-files") {
        landOfficer.markModified("companyInformationFiles");
        landOfficer.companyInformationFiles.uploadFiles = [...landOfficer.companyInformationFiles.uploadFiles, ...uploadedFiles];
      }
      else if (req.body.fileGroup === "stamp-duty-additional-information-files") {
        landOfficer.markModified("stampDutyAdditionalFiles");
        landOfficer.stampDutyAdditionalFiles.uploadFiles = [...landOfficer.stampDutyAdditionalFiles.uploadFiles, ...uploadedFiles];
      }

      await landOfficer.save();
    }
    return res.status(200).end();
  } catch (e) {
    console.log(e);
    return res.status(500).end();
  }
};

exports.validateRemission = async function (req, res) {
  try {
    const landOfficer = await ExemptionModel.findById(req.params.submissionId);
    const hasRemissionFiles = landOfficer && landOfficer.signedFiles.uploadFiles.length > 0;

    return res.status(200).json({ "success": true, hasRemissionFiles: hasRemissionFiles });

  } catch (e) {
    console.log(e);
    return res.status(500).end();
  }
};

exports.getStampDutyExemptionApplicationView = async function (req, res) {
  try {
    req.session.files = {};
    const application = await StampDutyApplicationModel.findById(req.params.submissionId);
    application.propertyDetails.parcelFirstPart = application.propertyDetails.parcel.split('/')[0];
    application.propertyDetails.parcelSecondPart = application.propertyDetails.parcel.split('/')[1];


    const filesInformation = {
      affidavit: { name: "Affidavit", files: application.affidavit, filesCount: application?.affidavit.length, validated: application.psOfficerTier ? application.psOfficerTier?.validations?.affidavit : false },
      marriageCertificates: { name: "Marriage Certificates", files: application.marriageCertificates, filesCount: application?.marriageCertificates.length, validated: application.psOfficerTier ? application.psOfficerTier?.validations?.marriageCertificates : false },
      birthCertificate: { name: "Copy of applicant s TCI birth certificate and a valid Government issued identification", files: application.birthCertificate, filesCount: application?.birthCertificate.length, validated: application.psOfficerTier ? application.psOfficerTier?.validations?.birthCertificate : false },
      parentsDocuments: { name: "Parent’s documents", files: application.parentsDocuments, filesCount: application?.parentsDocuments.length, validated: application.psOfficerTier ? application.psOfficerTier?.validations?.parentsDocuments : false },
      signedAgreement: { name: "Signed agreement of the purchase of the property", files: application.signedAgreement, filesCount: application?.signedAgreement.length, validated: application.psOfficerTier ? application.psOfficerTier?.validations?.signedAgreement : false },
      landRegister: { name: "A certified copy of the Land Register Extract", files: application.landRegister, filesCount: application?.landRegister.length, validated: application.psOfficerTier ? application.psOfficerTier?.validations?.landRegister : false },
      valuation: { name: "Valuation", files: application.valuation, filesCount: application?.valuation.length, validated: application.psOfficerTier ? application.psOfficerTier?.validations?.valuation : false },
      statusCard: { name: "Turks & Caicos Islander Status Card", files: application.statusCard, filesCount: application?.statusCard.length, validated: application.psOfficerTier ? application.psOfficerTier?.validations?.statusCard : false },
      botcCertificate: { name: "Copy of BOTC certificate along with a Government issued identification", files: application.botcCertificate, filesCount: application?.botcCertificate.length, validated: application.psOfficerTier ? application.psOfficerTier?.validations?.botcCertificate : false },
    }
    let applicants = [{
      firstName: application.firstName,
      lastName: application.lastName,
      dateOfBirth: application.dateOfBirth,
      statusCardNumber: application.statusCardNumber,
    }];

    if (application.applicants && application.applicants.length) {
      applicants.push(...application.applicants.map(ap => {
        return {
          firstName: ap.firstName,
          lastName: ap.lastName,
          dateOfBirth: ap.dateOfBirth,
        }
      }));
    }


    applicants = await Promise.all(applicants.map(async (ap) => {
      // Search in import duty waiver submissions
      const previousImportDutyApplications = await ImportDutyWaiverApplicationModel.find({
        '$or': [
          {
            'lastName': ap.lastName,
            'dateOfBirth': ap.dateOfBirth,
          },
          {
            'applicants.lastName': ap.lastName,
            'applicants.dateOfBirth': ap.dateOfBirth,
          },
          {
            'statusCardNumber': { '$exists': true, '$nin': ["", null], '$eq': ap.statusCardNumber },
          }
        ],
      });
      // Search in import duty waiver submissions
      const previousStampDutyExemptionApplications = await StampDutyApplicationModel.find({
        '$or': [
          {
            'lastName': ap.lastName,
            'dateOfBirth': ap.dateOfBirth,
          },
          {
            'applicants.lastName': ap.lastName,
            'applicants.dateOfBirth': ap.dateOfBirth,
          },
          {
            'statusCardNumber': { '$exists': true, '$nin': ["", null], '$eq': ap.statusCardNumber },
          }
        ],
      });
      ap.previousApplications = {
        importDuty: previousImportDutyApplications,
        stampDutyExemption: previousStampDutyExemptionApplications,
      };
      ap.totalValue =
        [
          previousImportDutyApplications.reduce((a, b) => a + b.remittedAmount || 0, 0),
          previousStampDutyExemptionApplications.reduce((a, b) => a + b.remittedAmount || 0, 0),
        ].reduce((a, b) => a + b, 0);
      return ap;
    }));

    const { foundCardHolder, statusInformationColor } = await statusCardHolderHelper.getCardHolderStatus(application);

    res.render('ps-submissions/open-stamp-duty-exemption-form',
      {
        application: application,
        validations: application.psOfficerTier ? application.psOfficerTier.validations : null,
        islands,
        calendar,
        applicants,
        filesInformation,
        foundCardHolder,
        statusInformationColor
      });

  } catch (e) {
    console.log(e);
    return res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).end();
  }
};

exports.getImportDutyWaiverApplicationView = async function (req, res) {
  try {
    req.session.files = {};
    const application = await ImportDutyWaiverApplicationModel.findById(req.params.submissionId);
    application.propertyDetails.parcelFirstPart = application.propertyDetails.parcel.split('/')[0];
    application.propertyDetails.parcelSecondPart = application.propertyDetails.parcel.split('/')[1];
    const filesInformation = {
      affidavit: { name: "Affidavit", files: application.affidavit, filesCount: application.affidavit.length, validated: application.deputyCommissionerTier ? application.deputyCommissionerTier.validations?.affidavit : false },
      marriageCertificates: { name: "Marriage Certificates", files: application.marriageCertificates, filesCount: application.marriageCertificates.length, validated: application.deputyCommissionerTier ? application.deputyCommissionerTier.validations?.marriageCertificates : false },
      birthCertificate: { name: "Copy of applicant s TCI birth certificate and a valid Government issued identification", files: application.birthCertificate, filesCount: application.birthCertificate.length, validated: application.deputyCommissionerTier ? application.deputyCommissionerTier.validations?.birthCertificate : false },
      parentsDocuments: { name: "Parent’s documents", files: application.parentsDocuments, filesCount: application.parentsDocuments.length, validated: application.deputyCommissionerTier ? application.deputyCommissionerTier.validations?.parentsDocuments : false },
      itemsInvoices: { name: "Copies of invoices", files: application.itemsInvoices, filesCount: application.itemsInvoices.length, validated: application.deputyCommissionerTier ? application.deputyCommissionerTier.validations?.itemsInvoices : false },
      buildingPictures: { name: "Building photographs", files: application.buildingPictures, filesCount: application.buildingPictures.length, validated: application.deputyCommissionerTier ? application.deputyCommissionerTier.validations?.buildingPictures : false },
      buildingPermit: { name: "Building permit", files: application.buildingPermit, filesCount: application.buildingPermit.length, validated: application.deputyCommissionerTier ? application.deputyCommissionerTier.validations?.buildingPermit : false },
      landRegister: { name: "A certified copy of the Land Register Extract", files: application.landRegister, filesCount: application.landRegister.length, validated: application.deputyCommissionerTier ? application.deputyCommissionerTier.validations?.landRegister : false },
      valuation: { name: "Valuation", files: application.valuation, filesCount: application.valuation.length, validated: application.deputyCommissionerTier ? application.deputyCommissionerTier.validations?.valuation : false },
      statusCard: { name: "Turks & Caicos Islander Status Card", files: application.statusCard, filesCount: application.statusCard.length, validated: application.deputyCommissionerTier ? application.deputyCommissionerTier.validations?.statusCard : false },
      botcCertificate: { name: "Copy of BOTC certificate along with a Government issued identification", files: application.botcCertificate, filesCount: application.botcCertificate.length, validated: application.deputyCommissionerTier ? application.deputyCommissionerTier.validations?.botcCertificate : false },
    }
    let applicants = [{
      firstName: application.firstName,
      lastName: application.lastName,
      dateOfBirth: application.dateOfBirth,
      statusCardNumber: application.statusCardNumber,
    }];

    if (application.applicants && application.applicants.length) {
      applicants.push(...application.applicants.map(ap => {
        return {
          firstName: ap.firstName,
          lastName: ap.lastName,
          dateOfBirth: ap.dateOfBirth,
        }
      }));
    }


    applicants = await Promise.all(applicants.map(async (ap) => {
      // Search in import duty waiver submissions
      const previousImportDutyApplications = await ImportDutyWaiverApplicationModel.find({
        '$or': [
          {
            'lastName': ap.lastName,
            'dateOfBirth': ap.dateOfBirth,
          },
          {
            'applicants.lastName': ap.lastName,
            'applicants.dateOfBirth': ap.dateOfBirth,
          },
          {
            'statusCardNumber': { '$exists': true, '$nin': ["", null], '$eq': ap.statusCardNumber },
          }
        ],
      });
      // Search in import duty waiver submissions
      const previousStampDutyExemptionApplications = await StampDutyApplicationModel.find({
        '$or': [
          {
            'lastName': ap.lastName,
            'dateOfBirth': ap.dateOfBirth,
          },
          {
            'applicants.lastName': ap.lastName,
            'applicants.dateOfBirth': ap.dateOfBirth,
          },
          {
            'statusCardNumber': { '$exists': true, '$nin': ["", null], '$eq': ap.statusCardNumber },
          }
        ],
      });
      ap.previousApplications = {
        importDuty: previousImportDutyApplications,
        stampDutyExemption: previousStampDutyExemptionApplications,
      }
      ap.totalValue =
        [
          previousImportDutyApplications.reduce((a, b) => a + b.remittedAmount || 0, 0),
          previousStampDutyExemptionApplications.reduce((a, b) => a + b.remittedAmount || 0, 0),
        ].reduce((a, b) => a + b, 0);
      return ap;
    }));

    const { foundCardHolder, statusInformationColor } = await statusCardHolderHelper.getCardHolderStatus(application);

    res.render('ps-submissions/open-import-duty-form',
      {
        application: application,
        islands,
        calendar,
        applicants,
        filesInformation,
        foundCardHolder,
        statusInformationColor
      });

  } catch (e) {
    console.log(e);
    return res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).end();
  }
};

exports.saveStampDutyExemptionApplication = async function (req, res) {
  try {
    const validation = {
      applicantDetails: req.body.validatedApplicantDetails === 'Yes',
      personalDetails: req.body.validatedPersonalDetails === 'Yes',
      additionalApplicants: req.body.validatedAdditionalApplicants === 'Yes',
      propertyDetails: req.body.validatedPropertyDetails === 'Yes',
      sellersDetails: req.body.validatedSellersDetails === 'Yes',
      affidavit: req.body.validated_affidavit === 'Yes',
      marriageCertificates: req.body.validated_marriageCertificates === 'Yes',
      birthCertificate: req.body.validated_birthCertificate === 'Yes',
      parentsDocuments: req.body.validated_parentsDocuments === 'Yes',
      signedAgreement: req.body.validated_signedAgreement === 'Yes',
      landRegister: req.body.validated_landRegister === 'Yes',
      valuation: req.body.validated_valuation === 'Yes',
      statusCard: req.body.validated_statusCard === 'Yes',
      botcCertificate: req.body.validated_botcCertificate === 'Yes',
    };

    await StampDutyApplicationModel.updateOne({ _id: req.params.submissionId }, {
      'psOfficerTier.validations': validation,
      'psOfficerTier.username': req.session.user.username,
      status: 'SAVED PS OFFICER',
      remittedAmount: req.body.remittedAmount ? req.body.remittedAmount : 0
    });



    return res.status(httpConstants.HTTP_STATUS_OK).json({ "success": true });

  } catch (e) {
    console.log("error: ", e);
    res.redirect('/stamp-duty/dashboard');
  }
};

exports.approveStampDutyExemptionApplication = async function (req, res) {
  try {
    const validation = {
      applicantDetails: req.body.validatedApplicantDetails === 'Yes',
      personalDetails: req.body.validatedPersonalDetails === 'Yes',
      additionalApplicants: req.body.validatedAdditionalApplicants === 'Yes',
      propertyDetails: req.body.validatedPropertyDetails === 'Yes',
      sellersDetails: req.body.validatedSellersDetails === 'Yes',
      affidavit: req.body.validated_affidavit === 'Yes',
      marriageCertificates: req.body.validated_marriageCertificates === 'Yes',
      birthCertificate: req.body.validated_birthCertificate === 'Yes',
      parentsDocuments: req.body.validated_parentsDocuments === 'Yes',
      signedAgreement: req.body.validated_signedAgreement === 'Yes',
      landRegister: req.body.validated_landRegister === 'Yes',
      valuation: req.body.validated_valuation === 'Yes',
      statusCard: req.body.validated_statusCard === 'Yes',
      botcCertificate: req.body.validated_botcCertificate === 'Yes',
    };

    const submission = await StampDutyApplicationModel.findById(req.params.submissionId);
    const comment = {
      date: new Date(),
      internalComments: req.body.internalComments,
      user: req.session.user.username,
      status: 'APPROVED',
    };
    if (submission.psOfficerTier && submission.psOfficerTier.comments && submission.psOfficerTier.comments.length) {
      submission.psOfficerTier.comments.push(comment);
    } else {
      submission.psOfficerTier = {
        comments: [comment],
      }
    }
    await StampDutyApplicationModel.updateOne({ _id: req.params.submissionId }, {
      'psOfficerTier.validations': validation,
      'psOfficerTier.approvedAt': new Date(),
      'psOfficerTier.username': req.session.user.username,
      'psOfficerTier.comments': submission.psOfficerTier.comments,
      status: 'APPROVED',
      remittedAmount: req.body.remittedAmount ? req.body.remittedAmount : 0
    });

    const email = submission.filingYourself ? submission.email : submission.personalInformation?.email;
    let emailContent = await MailFormatter.generateEmailStampDutyExemptionApplicationApprove(submission);
    await MailController.asyncSend([email, process.env.EMAIL_LAND_OFFICER_RECIPIENT],
      'Stamp Duty Exemption Application approved',
      emailContent.textString,
      emailContent.htmlString,
      emailContent.pdf,
      "Official Letter of Approval - Application for Home Owner Policy.pdf"
    );


    return res.status(httpConstants.HTTP_STATUS_OK).json({
      "status": httpConstants.HTTP_STATUS_OK,
      "message": "Your application has been updated successfully"
    });

  } catch (e) {
    console.log("error: ", e);
    return res.status(httpConstants.HTTP_STATUS_OK).json({
      "status": httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR,
      "message": "There was an error updating the status of the application, try again later..."
    });
  }
};

exports.returnStampDutyExemptionApplication = async function (req, res) {
  try {
    const submission = await StampDutyApplicationModel.findById(req.params.submissionId);
    const comment = {
      date: new Date(),
      internalComments: req.body.internalComments,
      declineReason: req.body.declineReason,
      user: req.session.user.username,
      status: 'RETURNED',
    };
    if (submission.psOfficerTier && submission.psOfficerTier.comments && submission.psOfficerTier.comments.length) {
      submission.psOfficerTier.comments.push(comment);
    } else {
      submission.psOfficerTier = {
        comments: [comment],
      }
    }

    await StampDutyApplicationModel.updateOne({ _id: req.params.submissionId }, {
      'psOfficerTier.declinedAt': new Date(),
      'psOfficerTier.username': req.session.user.username,
      'psOfficerTier.comments': submission.psOfficerTier.comments,
      status: 'RETURNED BY PS',
    });

    return res.status(httpConstants.HTTP_STATUS_OK).json({
      "status": httpConstants.HTTP_STATUS_OK,
      "message": "Your application has been updated successfully"
    });

  } catch (e) {
    console.log("error: ", e);
    return res.status(httpConstants.HTTP_STATUS_OK).json({
      "status": httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR,
      "message": "There was an error updating the status of the application, try again later..."
    });
  }
};


exports.getReductionApplicationView = async function (req, res) {
  try {
    req.session.files = {};
    const application = await ReductionApplicationModel.findById(req.params.submissionId);
    application.propertyDetails.parcelFirstPart = application.propertyDetails.parcel.split('/')[0];
    application.propertyDetails.parcelSecondPart = application.propertyDetails.parcel.split('/')[1];


    const filesInformation = {
      affidavit: { name: "Affidavit", files: application.affidavit, filesCount: application.affidavit.length, validated: application.psOfficerTier ? application.psOfficerTier.validations?.affidavit : false },
      marriageCertificates: { name: "Marriage Certificates", files: application.marriageCertificates, filesCount: application.marriageCertificates.length, validated: application.psOfficerTier ? application.psOfficerTier.validations?.marriageCertificates : false },
      birthCertificate: { name: "Copy of applicant s TCI birth certificate and a valid Government issued identification", files: application.birthCertificate, filesCount: application.birthCertificate.length, validated: application.psOfficerTier ? application.psOfficerTier.validations?.birthCertificate : false },
      parentsDocuments: { name: "Parent’s documents", files: application.parentsDocuments, filesCount: application.parentsDocuments.length, validated: application.psOfficerTier ? application.psOfficerTier.validations?.parentsDocuments : false },
      landRegister: { name: "A certified copy of the Land Register Extract", files: application.landRegister, filesCount: application.landRegister.length, validated: application.psOfficerTier ? application.psOfficerTier.validations?.landRegister : false },
      valuation: { name: "Valuation", files: application.valuation, filesCount: application.valuation.length, validated: application.psOfficerTier ? application.psOfficerTier.validations?.valuation : false },
      statusCard: { name: "Turks & Caicos Islander Status Card", files: application.statusCard, filesCount: application.statusCard.length, validated: application.psOfficerTier ? application.psOfficerTier.validations?.statusCard : false },
      botcCertificate: { name: "Copy of BOTC certificate along with a Government issued identification", files: application.botcCertificate, filesCount: application.botcCertificate.length, validated: application.psOfficerTier ? application.psOfficerTier.validations?.botcCertificate : false },
    }

    const { foundCardHolder, statusInformationColor } = await statusCardHolderHelper.getCardHolderStatusReduction(application);

    res.render('ps-submissions/open-stamp-duty-reduction-form',
      {
        application: application,
        validations: application.psOfficerTier ? application.psOfficerTier.validations : null,
        islands,
        calendar,
        filesInformation,
        foundCardHolder,
        statusInformationColor
      });

  } catch (e) {
    console.log(e);
    return res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).end();
  }
};

exports.saveReductionApplication = async function (req, res) {
  try {
    const validation = {
      applicantDetails: req.body.validatedApplicantDetails === 'Yes',
      additionalApplicants: req.body.validatedAdditionalApplicants === 'Yes',
      companyDetails: req.body.validatedCompanyDetails === 'Yes',
      propertyDetails: req.body.validatedPropertyDetails === 'Yes',
      affidavit: req.body.validated_affidavit === 'Yes',
      marriageCertificates: req.body.validated_marriageCertificates === 'Yes',
      birthCertificate: req.body.validated_birthCertificate === 'Yes',
      parentsDocuments: req.body.validated_parentsDocuments === 'Yes',
      signedAgreement: req.body.validated_signedAgreement === 'Yes',
      landRegister: req.body.validated_landRegister === 'Yes',
      valuation: req.body.validated_valuation === 'Yes',
      statusCard: req.body.validated_statusCard === 'Yes',
      botcCertificate: req.body.validated_botcCertificate === 'Yes',
    };

    await ReductionApplicationModel.updateOne({ _id: req.params.submissionId }, {
      'psOfficerTier.validations': validation,
      'psOfficerTier.username': req.session.user.username,
      status: 'SAVED PS OFFICER',
      remittedAmount: req.body.remittedAmount ? req.body.remittedAmount : 0
    });

    return res.status(httpConstants.HTTP_STATUS_OK).json({ "success": true });

  } catch (e) {
    console.log("error: ", e);
    res.redirect('/stamp-duty/dashboard');
  }
};

exports.approveReductionApplication = async function (req, res) {
  try {
    const validation = {
      applicantDetails: req.body.validatedApplicantDetails === 'Yes',
      additionalApplicants: req.body.validatedAdditionalApplicants === 'Yes',
      companyDetails: req.body.validatedCompanyDetails === 'Yes',
      propertyDetails: req.body.validatedPropertyDetails === 'Yes',
      affidavit: req.body.validated_affidavit === 'Yes',
      marriageCertificates: req.body.validated_marriageCertificates === 'Yes',
      birthCertificate: req.body.validated_birthCertificate === 'Yes',
      parentsDocuments: req.body.validated_parentsDocuments === 'Yes',
      signedAgreement: req.body.validated_signedAgreement === 'Yes',
      landRegister: req.body.validated_landRegister === 'Yes',
      valuation: req.body.validated_valuation === 'Yes',
      statusCard: req.body.validated_statusCard === 'Yes',
      botcCertificate: req.body.validated_botcCertificate === 'Yes',
    };

    const submission = await ReductionApplicationModel.findById(req.params.submissionId);
    const comment = {
      date: new Date(),
      internalComments: req.body.internalComments,
      user: req.session.user.username,
      status: 'APPROVED',
    };
    if (submission.psOfficerTier && submission.psOfficerTier.comments && submission.psOfficerTier.comments.length) {
      submission.psOfficerTier.comments.push(comment);
    } else {
      submission.psOfficerTier = {
        comments: [comment],
      }
    }
    await ReductionApplicationModel.updateOne({ _id: req.params.submissionId }, {
      'psOfficerTier.validations': validation,
      'psOfficerTier.approvedAt': new Date(),
      'psOfficerTier.username': req.session.user.username,
      'psOfficerTier.comments': submission.psOfficerTier.comments,
      status: 'APPROVED',
      remittedAmount: req.body.remittedAmount ? req.body.remittedAmount : 0
    });

    const email = submission.filingBehalf === 'Natural person' ?
      submission.applicantDetails.email :
      submission.companyDetails.email;
    let emailContent = await MailFormatter.generateEmailReductionApplicationApprove(submission);
    await MailController.asyncSend([email, process.env.EMAIL_LAND_OFFICER_RECIPIENT],
      'Stamp Duty Rate Reduction Application approved',
      emailContent.textString,
      emailContent.htmlString,
      emailContent.pdf,
      "Official Letter of Approval - Application for Stamp Duty Rate Reduction.pdf"
    );


    return res.status(httpConstants.HTTP_STATUS_OK).json({
      "status": httpConstants.HTTP_STATUS_OK,
      "message": "Your application has been updated successfully"
    });

  } catch (e) {
    console.log("error: ", e);
    return res.status(httpConstants.HTTP_STATUS_OK).json({
      "status": httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR,
      "message": "There was an error updating the status of the application, try again later..."
    });
  }
};

exports.returnReductionApplication = async function (req, res) {
  try {
    const submission = await ReductionApplicationModel.findById(req.params.submissionId);
    const comment = {
      date: new Date(),
      internalComments: req.body.internalComments,
      declineReason: req.body.declineReason,
      user: req.session.user.username,
      status: 'RETURNED',
    };
    if (submission.psOfficerTier && submission.psOfficerTier.comments && submission.psOfficerTier.comments.length) {
      submission.psOfficerTier.comments.push(comment);
    } else {
      submission.psOfficerTier = {
        comments: [comment],
      }
    }

    await ReductionApplicationModel.updateOne({ _id: req.params.submissionId }, {
      'psOfficerTier.declinedAt': new Date(),
      'psOfficerTier.username': req.session.user.username,
      'psOfficerTier.comments': submission.psOfficerTier.comments,
      status: 'RETURNED BY PS',
    });

    return res.status(httpConstants.HTTP_STATUS_OK).json({
      "status": httpConstants.HTTP_STATUS_OK,
      "message": "Your application has been updated successfully"
    });

  } catch (e) {
    console.log("error: ", e);
    return res.status(httpConstants.HTTP_STATUS_OK).json({
      "status": httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR,
      "message": "There was an error updating the status of the application, try again later..."
    });
  }
};

