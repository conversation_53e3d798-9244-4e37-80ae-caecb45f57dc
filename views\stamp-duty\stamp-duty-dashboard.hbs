<main class="">
  <div class="container">
    <div class="row">
      <div class="col-12">
        <div class="card-box">
          <div class="card-title">
            <h2>The Collector of Stamp Duty (dashboard)</h2>
          </div>
          <div class="row mt-3">
            <div class="col-12 ml-1">
              <h4>Search filters:</h4>
            </div>
          </div>
          <div class="row p-1">
            <div class="col-md-11">
              <form method="POST" id="searchForm">
                <div class="row my-2">
                  <div class="col-md-4">
                    <label for="exemptionType">Exemption type</label>
                    <select name="exemptionType" id='exemptionType' class="form-control custom-select" onchange="">
                      <option value="" selected>Select...</option>
                      <option id="all" value="all" {{#ifEquals filters.exemptionType 'all' }} selected {{/ifEquals}}>All
                      </option>
                      <option id="love-and-affection" value="love-and-affection" {{#ifEquals
                        filters.exemptionType 'love-and-affection' }} selected {{/ifEquals}}>
                        Natural Love &
                        Affection
                      </option>
                      <option id="section-exemptions" value="section-exemptions" {{#ifEquals
                        filters.exemptionType 'section-exemptions' }} selected {{/ifEquals}}>
                        Section 23 & 28
                        Exemptions
                      </option>
                      <option id="charitable-institution" value="charitable-institution" {{#ifEquals
                        filters.exemptionType 'charitable-institution' }} selected {{/ifEquals}}>Transfers
                        to Charitable Institutions
                      </option>
                      <option id="transmission" value="transmission" {{#ifEquals filters.exemptionType 'transmission' }}
                        selected {{/ifEquals}}>
                        Transmission
                      </option>
                      <option id="refunds" value="refunds" {{#ifEquals filters.exemptionType 'refunds' }} selected
                        {{/ifEquals}}>Refunds
                      </option>
                      <option id="remissions" value="remissions" {{#ifEquals filters.exemptionType 'remissions' }}
                        selected {{/ifEquals}}>Remissions
                      </option>
                      <option id="exemption-application" value="exemption-application" {{#ifEquals
                        filters.exemptionType 'exemption-application' }} selected {{/ifEquals}}>
                        Stamp Duty Exemption
                      </option>
                      <option id="import-duty-waiver" value="Import Duty Waiver" {{#ifEquals
                        filters.exemptionType 'Import Duty Waiver' }}selected {{/ifEquals}}>
                        Import Duty Waiver
                      </option>
                      <option id="stamp-duty-reduction" value="Stamp Duty Reduction" {{#ifEquals
                        filters.exemptionType 'Stamp Duty Reduction' }} selected {{/ifEquals}}>
                        Stamp Duty Reduction
                      </option>
                      <label for="exemptionType"></label>
                    </select>
                  </div>
                  <div class="col-md-4">
                    <label for="auditReady">Audit ready?</label>
                    <select name="auditReady" id='auditReady' class="form-control custom-select">
                      <option value="" {{#ifEquals filters.auditReady '' }} selected {{/ifEquals}}>All</option>
                      <option value="Yes" {{#ifEquals filters.auditReady 'Yes' }} selected {{/ifEquals}}>Yes</option>
                      <option value="No" {{#ifEquals filters.auditReady 'No' }} selected {{/ifEquals}}>No</option>
                    </select>
                  </div>
                  <div class="col-md-4">
                    <label for="exemptionType">Search</label>
                    <input class='form-control' type='text' name='searchFilter' id="searchFilter"
                      placeholder="Enter at least 3 characters" id='search_filter' value="{{filters.searchFilter}}" />
                    <label for="search_filter"></label>
                  </div>
                </div>
                <div class="row align-items-end mb-3">
                  <div class="col-md-6">
                    <label for="submittedStart">Submitted</label>
                    <div class="input-group">
                      <div class="input-group-prepend">
                        <span class="input-group-text" id="money-addon">From</span>
                      </div>
                      <input class="form-control input-group-append" type="date" name="submittedStart"
                        id="submittedStart" form="searchForm" value="{{filters.submittedStart}}" />
                      <div class="input-group-append">
                        <span class="input-group-text" id="money-addon">To</span>
                      </div>
                      <input class="form-control input-group-append" type="date" name="submittedEnd" id="submittedEnd"
                        form="searchForm" value="{{filters.submittedEnd}}" />
                    </div>
                  </div>
                  <div class="col-md-3">
                    <div class="custom-control custom-checkbox text-center">
                      <input type="checkbox" name="showPendingActions" class="custom-control-input"
                        id="showPendingActions" form="searchForm" {{#if filters.showPendingActions}} checked {{/if}}>
                      <label class="custom-control-label mt-1" for="showPendingActions">Show only pending
                        actions</label>
                    </div>
                  </div>
                  <div class="col-md-3">
                    <button type="submit" class='btn btn-light btn-sm waves-effect'>Search</button>
                    <input type="button" class='btn btn-light btn-sm' value="Clear"
                      onclick="$('#searchForm .form-control').val('')"></input>
                  </div>
                </div>
              </form>
            </div>
          </div>

          <div class="card-body">

            <!-- AUTOMATICALY ASSIGNED TABLE -->
            <div id="automatic-assign-table" class="row">
              <div class="table">
                <table id="scroll-horizontal-datatable" class="table table-striped w-100 nowrap">
                  <thead>
                    <tr>
                      <th class="tableYellow">Actions</th>
                      <th class="tableYellow">Status</th>
                      <th class="tableYellow">Id</th>
                      <th class="tableYellow">Timestamp</th>
                      <th class="tableYellow">Date submitted</th>
                      <th class="tableYellow">Reference Nr</th>
                      <th class="tableYellow">Applicant</th>
                      <th class="tableYellow">Parcel #</th>
                      <th class="tableYellow">District</th>
                      <th class="tableYellow">Island</th>
                      <th class="tableYellow">Remitted Amount</th>
                      <th class="tableYellow">Exemption type</th>
                    </tr>
                  </thead>
                  <tbody>
                    {{#each landOfficers}}
                    <tr class="exemptions-row">
                      <td>{{#if hasActionsPending}}<i class="fas fa-exclamation-triangle text-danger"></i>{{/if}}</td>
                      <td>{{ status }}</td>
                      <td>{{ _id }}</td>
                      <td>{{formatDate createdAt "x"}}</td>
                      <td>{{formatDate createdAt "DD/MM/YYYY"}}</td>
                      <td></td>
                      <td>{{ transferorName }}</td>
                      <td>{{ parcelNumber }}</td>
                      <td>{{ district }}</td>
                      <td>{{ island }}</td>
                      <td>$ {{ value }}</td>
                      <td>{{ exemptionType }}</td>
                    </tr>
                    {{/each}}
                    {{#each importDutyWaiverApplications}}
                    <tr class="duty-waiver-row">
                      <td>{{#if hasActionsPending}}<i class="fas fa-exclamation-triangle text-danger"></i>{{/if}}</td>
                      <td>{{ status }}</td>
                      <td>{{ _id }}</td>
                      <td>{{formatDate createdAt "x"}}</td>
                      <td>{{formatDate createdAt "DD/MM/YYYY"}}</td>
                      <td>{{referenceNr}}</td>
                      <td>{{ firstName }} {{lastName}}</td>
                      <td>{{ propertyDetails.parcel }}</td>
                      <td>{{ propertyDetails.district }}</td>
                      <td>{{ propertyDetails.island }}</td>
                      <td>$ {{formatNumber remittedAmount }}</td>
                      <td>Import Duty Waiver</td>
                    </tr>
                    {{/each}}
                    {{#each stampDutyExemptions}}
                    <tr class="exemptions-application-row">
                      <td>{{#if hasActionsPending}}<i class="fas fa-exclamation-triangle text-danger"></i>{{/if}}</td>
                      <td>{{ status }}</td>
                      <td>{{ _id }}</td>
                      <td>{{formatDate createdAt "x"}}</td>
                      <td>{{formatDate createdAt "DD/MM/YYYY"}}</td>
                      <td>{{referenceNr}}</td>
                      <td>{{ firstName }} {{lastName}}</td>
                      <td>{{ propertyDetails.parcel }}</td>
                      <td>{{ propertyDetails.district }}</td>
                      <td>{{ propertyDetails.island }}</td>
                      <td>$ {{formatNumber remittedAmount }}</td>
                      <td>Stamp Duty Exemption</td>
                    </tr>
                    {{/each}}
                    {{#each reductionApplications}}
                    <tr class="reduction-application-row">
                      <td>{{#if hasActionsPending}}<i class="fas fa-exclamation-triangle text-danger"></i>{{/if}}</td>
                      <td>{{ status }}</td>
                      <td>{{ _id }}</td>
                      <td>{{formatDate createdAt "x"}}</td>
                      <td>{{formatDate createdAt "DD/MM/YYYY"}}</td>
                      <td>{{referenceNr}}</td>
                      {{#ifEquals filingBehalf 'Company'}}
                      <td>{{ companyDetails.name }}</td>
                      {{else}}
                      <td>{{ applicantDetails.firstName }} {{ applicantDetails.lastName}}</td>
                      {{/ifEquals}}
                      <td>{{ propertyDetails.parcel }}</td>
                      <td>{{ propertyDetails.district }}</td>
                      <td>{{ propertyDetails.island }}</td>
                      <td>$ {{formatNumber remittedAmount }}</td>
                      <td>Stamp Duty Reduction</td>
                    </tr>
                    {{/each}}
                  </tbody>
                </table>
              </div>
              <!-- RESPONSIVE TABLE END -->
            </div>
          </div>
          <!-- CONTENT END -->
          <div class="row mt-2 justify-content-between ">
            <a href="/stamp-duty/dashboard" class="btn btn-secondary width-lg waves-effect waves-light">
              Back
            </a>

            <button class="btn solid btn-warning" id="btn-export-xls">
              Export

            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</main>
<script type="text/javascript" src="/javascripts/export-xls.js"></script>
<script type="text/javascript">
  let table;
  let currentSelectedApplicationId;
  let status = '{{status}}';
  $(document).ready(function () {
    table = $("#scroll-horizontal-datatable").DataTable({
      "columnDefs": [
        { "visible": false, "targets": [2, 3] }, // Hide ID and Timestamp columns
        { "orderable": false, "targets": [0] }, // Actions column not orderable
        { "type": "date", "targets": [4] } // Date column for proper sorting
      ],
      "order": [[3, "desc"]], // Order by timestamp (hidden column) descending for most recent first
      scrollX: !0,
      select: { style: "single" },
      language: {
        paginate: {
          previous: "<i class='mdi mdi-chevron-left'>",
          next: "<i class='mdi mdi-chevron-right'>"
        }
      },
      drawCallback: function () {
        $(".dataTables_paginate > .pagination").addClass("pagination-rounded")
      }
    });
    table.on('select', function (e, dt, type, indexes) {
      if (type === 'row') {
        let selectedRowData = table.rows(indexes).data();
        currentSelectedApplicationId = selectedRowData[0][2]; // ID is now in column 2

        if (table.row(indexes[0]).node().className.includes('duty-waiver-row')) {
          window.location.href = '/stamp-duty/import-duty-waiver/' + currentSelectedApplicationId + "/open";
        } else if (table.row(indexes[0]).node().className.includes('exemptions-application-row')) {
          window.location.href = "/stamp-duty/exemption-application/" + currentSelectedApplicationId + "/update";
        } else if (table.row(indexes[0]).node().className.includes('reduction-application-row')) {
          window.location.href = "/stamp-duty/reduction-application/" + currentSelectedApplicationId + "/update";
        }
        else {
          window.location.href = "/stamp-duty/" + currentSelectedApplicationId + "/open";
        }
      }
    });
  });

  $('#btn-export-xls').click(function () {
    exportXlsFile("stampDutyOfficer", status ? status : '');
  });
</script>