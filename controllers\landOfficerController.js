const ExemptionModel = require('../models/exemptions');
const ConfigModel = require('../models/config');
const uploadController = require('../controllers/uploadController');
const landOfficerController = require('../controllers/landOfficerController');
const MailController = require('../controllers/mailController');
const MailFormatter = require('../controllers/mailFormatController');
const { v4: uuidv4 } = require('uuid');
const moment = require('moment');

function clearTempUploadFiles(req) {
  req.session.files = {};
}

function setFilesInformation(files, id, req) {
  files.forEach((file) => {
    file.present = !!(req.body.files && !!req.body.files[file.id] && req.body.files[file.id]["present"]);
    const uploadFiles = landOfficerController.getTempUploadFiles(req.session, file.id);

    if (uploadFiles) {
      uploadFiles.forEach((f) => {
        const name = file.internal.replace(/[\s’'/()]/g, '');
        uploadController.moveUpload(f, name, id);
        f.url = f.url.replace(name, id + "/" + name)
      });
      file.uploadFiles = [...file.uploadFiles, ...uploadFiles];
    }
  });

  return files;
}

exports.landOfficerDashboard = async function (req, res) {
  try {
    clearTempUploadFiles(req);
    let query = [];
    let status = [];
    let filters = {
      "searchFilter": req.body.searchFilter,
      "exemptionType": req.body.exemptionType,
      "searchDeclined": !!req.body.searchDeclined,
      "searchCompleted": !!req.body.searchCompleted,
      "searchSaved": !!req.body.searchSaved,
      "submittedEnd": req.body.submittedEnd,
      "submittedStart": req.body.submittedStart,
      "searchRequested": !!req.body.searchRequested,
    };
    let submissionDate = {};
    if (req.body.submittedStart) {
      submissionDate["createdAt"] = {
        $gte: req.body.submittedStart,
        $lte: req.body.submittedEnd ? moment(req.body.submittedEnd).add(1, 'd').toDate() : new Date(),
      };
      query.push(submissionDate);
    } else if (req.body.submittedEnd) {
      submissionDate["createdAt"] = { $lte: moment(req.body.submittedEnd).add(1, 'd').toDate() };
      query.push(submissionDate);
    }

    if (!req.body.searchDeclined && !req.body.searchCompleted && !req.body.searchSaved && !req.body.searchRequested &&
      !req.body.searchSubmitted) {
      status.push('SAVED', 'REQUESTED', 'APPROVED PS', 'SUBMITTED')
    }

    if (req.body.searchDeclined) {
      status.push('DECLINED')
    }
    if (req.body.searchCompleted) {
      status.push('COMPLETED')
    }
    if (req.body.searchSaved) {
      status.push('SAVED')
    }
    if (req.body.searchRequested) {
      status.push('REQUESTED')
    }
    if (req.body.searchSubmitted) {
      status.push('SUBMITTED')
    }

    query.push({ "status": { $in: status } }, { "type": "LAND SUBMISSION" });

    if (req.body.searchFilter && req.body.searchFilter.length > 2) {
      query.push({
        $or: [{ 'transferorName': { $regex: req.body.searchFilter, $options: 'i' } },
        { 'transfereeName': { $regex: req.body.searchFilter, $options: 'i' } },
        { 'parcelNumber': { $regex: req.body.searchFilter, $options: 'i' } }]
      })
    }

    if (req.body.exemptionType === 'love-and-affection') {
      query.push({ "exemptionType": 'Natural Love & Affection' });

    } else if (req.body.exemptionType === 'section-exemptions') {
      query.push({ "exemptionType": { $in: ['Section 23- Companies & 28- Bodies Corporate', 'Section 23 & 28 Exemptions'] } });
    } else if (req.body.exemptionType === 'charitable-institution') {
      query.push({ "exemptionType": 'Transfers to Charitable Institutions' });
    } else if (req.body.exemptionType === 'transmission') {
      query.push({ "exemptionType": 'Transmission' });
    }
    else if (req.body.exemptionType === 'refunds') {
      query.push({ "exemptionType": 'Refunds' });
    }
    else if (req.body.exemptionType === 'remissions') {
      query.push({ "exemptionType": 'Remissions' });
    }

    let landOfficers = await ExemptionModel.find({ $and: query }).limit(100);


    res.render('land-officer/land-officer-dashboard',
      {
        landOfficers: landOfficers,
        filters: filters,
      });

  } catch (e) {
    console.log("error: ", e);
    res.redirect('/land-officer/dashboard');
  }
};

exports.openNewLandOfficerView = async function (req, res) {
  try {
    clearTempUploadFiles(req);
    const landOfficerTemplate = await ConfigModel.findOne({ templateType: "landOfficers" });
    let templateFiles = [];
    const group = req.query.group ? req.query.group : "";

    templateFiles = group === "natural-love-and-affection" ? landOfficerTemplate.naturalLoveFiles :
      group === "section-exemptions" ? landOfficerTemplate.exemptionFiles :
        group === "charitable-institution" ? landOfficerTemplate.transferInstitutionFiles :
          group === "transmission" ? landOfficerTemplate.transmissionFiles :
            group === "refunds" ? landOfficerTemplate.refundFiles :
              group === "remissions" ? landOfficerTemplate.remissionFiles : [];

    res.render('land-officer/create-land-officer',
      {
        group: group,
        templateFiles: templateFiles
      });
  } catch (e) {
    console.log(e);
    res.redirect('/land-officer/dashboard')
  }

};

exports.createLandOfficer = async function (req, res) {
  try {
    let selectedOption = '';
    let formFields = req.body;
    let files = [];
    const landOfficerTemplate = await ConfigModel.findOne({ templateType: "landOfficers" });
    if (req.body.relationType === 'natural-love-and-affection') {
      selectedOption = 'Natural Love & Affection';
      files = landOfficerTemplate.naturalLoveFiles;
    } else if (req.body.relationType === 'section-exemptions') {
      selectedOption = 'Section 23- Companies & 28- Bodies Corporate';
      files = landOfficerTemplate.exemptionFiles;
    } else if (req.body.relationType === 'charitable-institution') {
      selectedOption = 'Transfers to Charitable Institutions';
      files = landOfficerTemplate.transferInstitutionFiles;
    } else if (req.body.relationType === 'transmission') {
      selectedOption = 'Transmission';
      files = landOfficerTemplate.transmissionFiles;
    }
    else if (req.body.relationType === 'refunds') {
      selectedOption = 'Refunds';
      files = landOfficerTemplate.refundFiles;
    }
    else if (req.body.relationType === 'remissions') {
      selectedOption = 'Remissions';
      files = landOfficerTemplate.remissionFiles;
    }

    let landOfficer = new ExemptionModel({
      instrumentNumber: formFields.instrumentNumber,
      status: req.body.status === 'submit-application' ? "SUBMITTED" : "SAVED",
      transferorName: formFields.transferorName,
      transfereeName: formFields.transfereeName,
      parcelNumber: formFields.parcelNumber,
      parcelTextNumber: formFields.parcelTextNumber,
      district: formFields.district,
      island: formFields.island,
      value: formFields.value,
      exemptionType: selectedOption,
      createdAt: new Date(),
      type: "LAND SUBMISSION",
      files: files,
      signedFiles: {
        internal: "Signed-Evidences",
        external: "Signed Evidences",
        fileGroup: "signed-files",
      },
      companyInformationFiles: {
        internal: "Company-Information",
        external: "Company Information",
        fileGroup: "company-information-files",
      },
      stampDutyAdditionalFiles: {
        internal: "Stamp-Duty-Additional-Information",
        external: "Stamp Duty Additional Information",
        fileGroup: "stamp-duty-additional-information-files",
      },
    });

    if (req.body.status === 'submit-application') {
      landOfficer.comments.push({
        email: req.session.user.username,
        date: new Date(),
        commentStatus: landOfficer.status,
        comment: req.body.comment
      });

      landOfficer.submittedOfficer = {
        username: req.session.user.name,
        email: req.session.user.username,
        submittedDate: new Date(),
        comment: req.body.comment
      };
      //let emailText = req.body.comment;
    }

    landOfficer.files = setFilesInformation(landOfficer.files, landOfficer._id, req);
    await landOfficer.save();

    if (landOfficer.status === "SUBMITTED") {
      let email = MailFormatter.generateEmail(landOfficer);
      await MailController.asyncSend([process.env.EMAIL_STAMP_DUTY_OFFICER_RECIPIENT],
        'Stamp Duty Exemption Program',
        email.textString,
        email.htmlString
      );
    }

    return res.status(200).json({ "success": true })

  } catch (e) {
    console.log("error: ", e);
    return res.status(500).end()
  }
};

exports.submitReviewId = async function (req, res) {
  try {
    let landOfficer = await ExemptionModel.findById(req.params.id);
    if (landOfficer) {
      landOfficer.instrumentNumber = req.body.instrumentNumber;
      landOfficer.status = "SUBMITTED";
      landOfficer.transferorName = req.body.transferorName;
      landOfficer.transfereeName = req.body.transfereeName;
      landOfficer.parcelNumber = req.body.parcelNumber;
      landOfficer.parcelTextNumber = req.body.parcelTextNumber;
      landOfficer.district = req.body.district;
      landOfficer.island = req.body.island;
      landOfficer.value = req.body.value;
      landOfficer.comments.push({
        email: req.session.user.username,
        date: new Date(),
        commentStatus: landOfficer.status,
        comment: req.body.comment
      });

      landOfficer.submittedOfficer = {
        username: req.session.user.name,
        email: req.session.user.username,
        submittedDate: new Date(),
        comment: req.body.comment
      };

      landOfficer.signedFiles = {
        internal: "Signed-Evidences",
        external: "Signed Evidences",
        fileGroup: "signed-files",
      };

      landOfficer.companyInformationFiles = {
        internal: "Company-Information",
        external: "Company Information",
        fileGroup: "company-information-files",
      };
      landOfficer.stampDutyAdditionalFiles = {
        internal: "Stamp-Duty-Additional-Information",
        external: "Stamp Duty Additional Information",
        fileGroup: "stamp-duty-additional-information-files",
      };

      landOfficer.files = setFilesInformation(landOfficer.files, landOfficer._id, req);
      await landOfficer.save();

      if (landOfficer.status === "SUBMITTED") {
        let email = MailFormatter.generateEmail(landOfficer);
        await MailController.asyncSend([process.env.EMAIL_STAMP_DUTY_OFFICER_RECIPIENT],
          'Stamp Duty Exemption Program',
          email.textString,
          email.htmlString
        );
      }
    }

    return res.status(200).json({ "success": true });

  } catch (e) {
    console.log("error: ", e);
    res.redirect('/finance-officer/dashboard');
  }
};

exports.updateLandOfficer = async function (req, res) {
  try {
    let landOfficer = await ExemptionModel.findById(req.params.id);

    if (req.params.id) {
      landOfficer.instrumentNumber = req.body.instrumentNumber;
      landOfficer.transferorName = req.body.transferorName;
      landOfficer.transfereeName = req.body.transfereeName;
      landOfficer.parcelNumber = req.body.parcelNumber;
      landOfficer.parcelTextNumber = req.body.parcelTextNumber;
      landOfficer.district = req.body.district;
      landOfficer.island = req.body.island;
      landOfficer.value = req.body.value;
    }
    landOfficer.files = setFilesInformation(landOfficer.files, landOfficer._id, req);

    await landOfficer.save();
    return res.status(200).json({ "success": true })

  } catch (e) {
    console.log("error: ", e);
    res.redirect('/finance-officer/dashboard');
  }
};


exports.getLandOfficerView = async function (req, res) {
  try {
    clearTempUploadFiles(req);
    const landOfficer = await ExemptionModel.findById(req.params.id);
    let isEditable = false;
    if (landOfficer) {
      if (landOfficer.status === "SAVED" || landOfficer.status === "REQUESTED") {
        isEditable = true;
      }
    }

    res.render('land-officer/open-view-form',
      {
        landOfficer: landOfficer,
        isEditable: isEditable,
      });

  } catch (e) {
    console.log(e);
    return res.status(500).end();
  }
};

exports.storeFiles = function (req, res) {
  try {
    let sessData = req.session;

    if (!sessData.files) {
      sessData.files = {}
    }
    const fileName = req.body.fileName;
    req.files.fileUploaded.forEach((file) => {
      uploadController.moveUpload(file, fileName).catch((reason => {
        if (reason) {
          console.log(reason);
        }
      }));
      file = {
        fileId: uuidv4(),
        fileTypeId: req.body.fileTypeId,
        fieldName: file.fieldname.replace(/fileUploaded/i, fileName),
        originalName: file.originalname,
        encoding: file.encoding,
        mimeType: file.mimetype,
        blobName: file.blobName.replace(/fileUploaded/i, fileName),
        container: file.container,
        blob: file.blob.replace(/fileUploaded/i, fileName),
        blobType: file.blobType,
        size: file.size,
        etag: file.etag,
        url: file.url.replace(/fileUploaded/i, fileName),
      };

      if (sessData.files && sessData.files[req.body.fileTypeId]) {
        let tempFiles = sessData.files[req.body.fileTypeId];
        if (tempFiles) {
          tempFiles.push(file);
        } else {
          tempFiles = [file];
        }
        sessData.files[req.body.fileTypeId] = tempFiles;
      } else {
        sessData.files[req.body.fileTypeId] = [file];
      }
    });
    return res.status(200).end();
  } catch (e) {
    return res.status(500).end();
  }

};

exports.deleteSavedApplication = async function (req, res) {
  try {
    let deleteId = req.params.id;
    const landOfficer = await ExemptionModel.deleteOne({ _id: deleteId });
    if (!landOfficer) {
      return res.status(404).json({ "success": false, "message": "Application not found" })
    }

    return res.status(200).json({ "success": true, })
  } catch (e) {
    console.log(e);
    return res.status(500).end();
  }
};

exports.getUploadedFiles = async function (req, res) {
  try {
    let filesToReturn = [];
    if (req.query.landOfficer) {
      const landOfficer = await ExemptionModel.findById(req.query.landOfficer);
      if (landOfficer) {
        let files;
        if (req.query.fileGroup) {
          if (req.query.fileGroup === "signed-files") {
            files = landOfficer.signedFiles.id === req.query.fileTypeId ? landOfficer.signedFiles : null;
          } else if (req.query.fileGroup === "transfer-files") {
            files = landOfficer.transferCompletedFiles.id === req.query.fileTypeId ? landOfficer.transferCompletedFiles : null;
          } else if (req.query.fileGroup === "company-information-files") {
            files = landOfficer.companyInformationFiles.id === req.query.fileTypeId ? landOfficer.companyInformationFiles : null;
          }
          else if (req.query.fileGroup === 'stamp-duty-additional-information-files') {
            files = landOfficer.stampDutyAdditionalFiles.id === req.query.fileTypeId ? landOfficer.stampDutyAdditionalFiles : null;
          }
        } else {
          files = landOfficer.files.find((f) => f.id === req.query.fileTypeId);
        }

        if (files && files.uploadFiles) {
          filesToReturn = [...filesToReturn, ...files.uploadFiles];
        }
      }
    }

    if (!req.query.fileGroup) {
      const tempFilesGroup = landOfficerController.getTempUploadFiles(req.session, req.query.fileTypeId);
      if (tempFilesGroup) {
        filesToReturn = [...filesToReturn, ...tempFilesGroup];
      }
    }

    return res.json({ success: true, files: filesToReturn });
  } catch (error) {
    console.log("error ", error);
    return res.status(500).end();
  }
};

exports.getTempUploadFiles = function (session, key) {
  if (key) {
    if (session.files && key in session.files) {
      return session.files[key];
    } else {
      return null;
    }
  } else {
    return session.files;
  }
}

exports.setTempUploadFiles = function (req, key, value) {
  if (req.session && req.session.files && req.session.files[key]) {
    return req.session.files[key] = value;
  }
};
