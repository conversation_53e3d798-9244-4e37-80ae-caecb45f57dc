<main class="">
  <div class="container">
    <div class="row">
      <div class="col-12">
        <div class="card-box">
          <div class="card-title">
            <h2>Dashboard Customs Officer</h2>
          </div>

          <div class="row mt-3">
            <div class="col-12 ml-1">
              <h4>Search filters:</h4>
            </div>
          </div>
          <div class="row p-1">
            <div class="col-md-11">
              <form method="POST" id="searchForm">
                <div class="row">
                  <div class="col-md-3">
                    <div class="custom-control custom-checkbox text-center">
                      <input type="checkbox" name="showPendingActions" class="custom-control-input"
                        id="showPendingActions" form="searchForm" {{#if filters.showPendingActions}} checked {{/if}}>
                      <label class="custom-control-label mt-1" for="showPendingActions">Show only pending
                        actions</label>
                    </div>
                  </div>
                  <div class="col-md-1">
                    <input type='SUBMIT' class='btn btn-light btn-sm waves-effect ' value='Search' />
                  </div>
                </div>
              </form>
            </div>
          </div>

          <div class="card-body">

            <!-- AUTOMATICALY ASSIGNED TABLE -->
            <div id="automatic-assign-table" class="row">
              <div class="table">
                <table id="scroll-horizontal-datatable" class="table table-striped w-100 nowrap">
                  <thead>
                    <tr>
                      <th class="tableYellow">Actions</th>
                      <th class="tableYellow">Status</th>
                      <th class="tableYellow">Id</th>
                      <th class="tableYellow">Timestamp</th>
                      <th class="tableYellow">Created At</th>
                      <th class="tableYellow">Reference Nr</th>
                      <th class="tableYellow">Applicant</th>
                      <th class="tableYellow">Parcel #</th>
                      <th class="tableYellow">District</th>
                      <th class="tableYellow">Island</th>
                      <th class="tableYellow">Revenue forgone</th>
                      <th class="tableYellow">Exemption type</th>
                    </tr>
                  </thead>
                  <tbody>
                    {{#each importDutyWaiverApplications}}
                    <tr class="duty-waiver-row">
                      <td>{{#if hasActionsPending}}<i class="fas fa-exclamation-triangle text-danger"></i>{{/if}}</td>
                      <td>{{ status }}</td>
                      <td>{{ _id }}</td>
                      <td>{{formatDate createdAt "x"}}</td>
                      <td>{{formatDate createdAt "DD/MM/YYYY"}}</td>
                      <td>{{referenceNr}}</td>
                      <td>{{ firstName }} {{lastName}}</td>
                      <td>{{ propertyDetails.parcel }}</td>
                      <td>{{ propertyDetails.district }}</td>
                      <td>{{ propertyDetails.island }}</td>
                      <td>$ {{formatNumber remittedAmount}}</td>
                      <td>Import Duty Waiver</td>
                    </tr>
                    {{/each}}
                  </tbody>
                </table>
              </div>
              <!-- RESPONSIVE TABLE END -->
            </div>
          </div>
          <!-- CONTENT END -->
          <div class="row mt-2 justify-content-between ">
            <div class="col-md-2">
              <a href="/../" class="btn btn-secondary width-lg waves-effect waves-light">
                Back
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</main>
<script type="text/javascript">
  let table;
  let currentSelectedApplicationId;
  $(document).ready(function () {
    table = $("#scroll-horizontal-datatable").DataTable({
      "columnDefs": [
        { "visible": false, "targets": [2, 3] }, // Hide ID and Timestamp columns
        { "orderable": false, "targets": [0] }, // Actions column not orderable
        { "type": "date", "targets": [4] } // Date column for proper sorting
      ],
      "order": [[3, "desc"]], // Order by timestamp (hidden column) descending for most recent first
      scrollX: !0,
      select: { style: "single" },
      language: {
        paginate: {
          previous: "<i class='mdi mdi-chevron-left'>",
          next: "<i class='mdi mdi-chevron-right'>"
        }
      },
      drawCallback: function () {
        $(".dataTables_paginate > .pagination").addClass("pagination-rounded")
      }
    });
    table.on('select', function (e, dt, type, indexes) {
      if (type === 'row') {
        let selectedRowData = table.rows(indexes).data();
        currentSelectedApplicationId = selectedRowData[0][2]; // ID is now in column 2
        if (table.row(indexes[0]).node().className.includes('duty-waiver-row')) {
          window.location.href = 'import-duty-waiver/' + currentSelectedApplicationId + "/update";
        }
      }
    });
  });
</script>