<main class="">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="card-box">
                    <div class="card-title">
                        <h2>Dashboard Land Officer</h2>
                    </div>

                    <div class="row mt-3">
                        <div class="col-12 ml-1">
                            <h4>Search filters:</h4>
                        </div>

                    </div>
                    <div class="row p-1">
                        <div class="col-md-11">
                            <form method="POST" id="searchForm">
                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="row">
                                            <div class="col-md-5">
                                                <select name="exemptionType" id='exemptionType'
                                                    class="form-control custom-select" onchange="">
                                                    <option value="" selected>Select...</option>
                                                    <option id="all" value="all" {{#ifEquals filters.exemptionType 'all'
                                                        }} selected {{/ifEquals}}>All
                                                    </option>
                                                    <option id="love-and-affection" value="love-and-affection"
                                                        {{#ifEquals filters.exemptionType 'love-and-affection' }}
                                                        selected {{/ifEquals}}>Natural Love &
                                                        Affection
                                                    </option>
                                                    <option id="section-exemptions" value="section-exemptions"
                                                        {{#ifEquals filters.exemptionType 'section-exemptions' }}
                                                        selected {{/ifEquals}}>Section 23 & 28
                                                        Exemptions
                                                    </option>
                                                    <option id="charitable-institution" value="charitable-institution"
                                                        {{#ifEquals filters.exemptionType 'charitable-institution' }}
                                                        selected {{/ifEquals}}>Transfers
                                                        to Charitable Institutions
                                                    </option>
                                                    <option id="transmission" value="transmission" {{#ifEquals
                                                        filters.exemptionType 'transmission' }} selected {{/ifEquals}}>
                                                        Transmission
                                                    </option>
                                                    <option id="refunds" value="refunds" {{#ifEquals
                                                        filters.exemptionType 'refunds' }} selected {{/ifEquals}}>
                                                        Refunds
                                                    </option>
                                                    <option id="remissions" value="remissions" {{#ifEquals
                                                        filters.exemptionType 'remissions' }} selected {{/ifEquals}}>
                                                        Remissions
                                                    </option>
                                                    <label for="exemptionType"></label>
                                                </select>
                                            </div>
                                            <div class="col-md-7">
                                                <input class='form-control' type='text' name='searchFilter'
                                                    placeholder="Enter at least 3 characters" id='search_filter'
                                                    value="{{filters.searchFilter}}" />
                                                <label for="search_filter"></label>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-12">
                                                <label for="submittedStart">Submitted</label>
                                                <div class="input-group mb-3">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text" id="money-addon">From</span>
                                                    </div>
                                                    <input class="form-control input-group-append" type="date"
                                                        name="submittedStart" id="submittedStart" form="searchForm"
                                                        value="{{filters.submittedStart}}" />
                                                    <div class="input-group-append">
                                                        <span class="input-group-text" id="money-addon">To</span>
                                                    </div>
                                                    <input class="form-control input-group-append" type="date"
                                                        name="submittedEnd" id="submittedEnd" form="searchForm"
                                                        value="{{filters.submittedEnd}}" />
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-3 text-left">
                                                <div class="custom-control custom-checkbox text-left">
                                                    <input type="checkbox" name="searchDeclined"
                                                        class="form-control custom-control-input" id="declinedCheck"
                                                        form="searchForm" {{#if filters.searchDeclined}} checked
                                                        {{/if}}>
                                                    <label class="custom-control-label mt-1" for="declinedCheck">Show
                                                        declined</label>
                                                </div>

                                                <div class="custom-control custom-checkbox text-left">
                                                    <input type="checkbox" name="searchCompleted"
                                                        class="form-control custom-control-input" id="searchCompleted"
                                                        form="searchForm" {{#if filters.searchCompleted}} checked
                                                        {{/if}}>
                                                    <label class="custom-control-label mt-1" for="searchCompleted">Show
                                                        completed</label>
                                                </div>
                                            </div>
                                            <div class="col-md-3 text-left">
                                                <div class="custom-control custom-checkbox text-left">
                                                    <input type="checkbox" name="searchSaved"
                                                        class="form-control custom-control-input" id="searchSaved"
                                                        form="searchForm" {{#if filters.searchSaved}} checked {{/if}}>
                                                    <label class="custom-control-label mt-1" for="searchSaved">Show
                                                        saved</label>
                                                </div>
                                                <div class="custom-control custom-checkbox text-left">
                                                    <input type="checkbox" name="searchSubmitted"
                                                        class="form-control custom-control-input" id="searchSubmitted"
                                                        form="searchForm" {{#if filters.searchSubmitted}} checked
                                                        {{/if}}>
                                                    <label class="custom-control-label mt-1" for="searchSubmitted">Show
                                                        submitted</label>
                                                </div>

                                            </div>

                                            <div class="col-md-6 text-left">
                                                <div class="custom-control custom-checkbox text-left">
                                                    <input type="checkbox" name="searchRequested"
                                                        class="form-control custom-control-input" id="searchRequested"
                                                        form="searchForm" {{#if filters.searchRequested}} checked
                                                        {{/if}}>
                                                    <label class="custom-control-label mt-1" for="searchRequested">Show
                                                        information requested</label>
                                                </div>

                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-3">
                                        <button type="submit" class='btn btn-light btn-sm waves-effect'>Search</button>
                                        <input type="button" class='btn btn-light btn-sm' value="Clear"
                                            onclick="$('#searchForm .form-control').val('').prop('checked', false)"></input>
                                    </div>
                                </div>
                            </form>
                        </div>

                        <div class="col-md-1">
                            <a href="create" class="btn btn-success"
                                style="background-color: #2fb339; border-color: #2bbf1a;">NEW</a>
                        </div>
                    </div>
                    <div class="card-body">

                        <!-- AUTOMATICALY ASSIGNED TABLE -->
                        <div id="automatic-assign-table" class="row">
                            <div class="table">
                                <table id="scroll-horizontal-datatable" class="table table-striped w-100 nowrap">
                                    <thead>
                                        <tr>
                                            <th class="tableYellow">Id
                                            </th>
                                            <th class="tableYellow">Date submitted
                                            </th>
                                            <th class="tableYellow">Name Transferor
                                            </th>
                                            <th class="tableYellow">Name Transferee
                                            </th>
                                            <th class="tableYellow">Parcel #
                                            </th>
                                            <th class="tableYellow">District
                                            </th>
                                            <th class="tableYellow">Island
                                            </th>
                                            <th class="tableYellow">Value
                                            </th>
                                            <th class="tableYellow">Exemption type
                                            </th>
                                            <th class="tableYellow">Status
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {{#each landOfficers}}
                                        <tr>
                                            <td>{{ _id }}</td>
                                            <td>{{formatDate createdAt "DD/MM/YYYY"}}</td>
                                            <td>{{ transferorName }}</td>
                                            <td>{{ transfereeName }}</td>
                                            <td>{{ parcelNumber }}</td>
                                            <td>{{ district }}</td>
                                            <td>{{ island }}</td>
                                            <td>$ {{ value }}</td>
                                            <td>{{ exemptionType }}</td>
                                            <td>{{ status }}</td>
                                        </tr>
                                        {{/each}}
                                    </tbody>
                                </table>
                            </div>
                            <!-- RESPONSIVE TABLE END -->
                        </div>
                    </div>
                    <!-- CONTENT END -->
                    <div class="row mt-2 justify-content-between ">
                        <a href="/../" class="btn btn-secondary width-lg waves-effect waves-light">
                            Back
                        </a>

                        <button class="btn solid btn-warning" id="btn-export-xls">
                            Export

                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>
<script type="text/javascript" src="/javascripts/export-xls.js"></script>

<script type="text/javascript">
    let table;
    let currentSelectedApplicationId;
    $(document).ready(function () {
        table = $("#scroll-horizontal-datatable").DataTable({
            "columnDefs": [{ "visible": false, "targets": [0] }],
            "order": [[3, "asc"]],
            scrollX: !0,
            select: { style: "single" },
            language: {
                paginate: {
                    previous: "<i class='mdi mdi-chevron-left'>",
                    next: "<i class='mdi mdi-chevron-right'>"
                }
            },
            drawCallback: function () {
                $(".dataTables_paginate > .pagination").addClass("pagination-rounded")
            }
        });
        table.on('select', function (e, dt, type, indexes) {
            if (type === 'row') {
                let selectedRowData = table.rows(indexes).data();
                currentSelectedApplicationId = selectedRowData[0][0];
                window.location.href = currentSelectedApplicationId + "/open";
            }
        });
    });

    $('#btn-export-xls').click(function () {
        exportXlsFile("landOfficer");
    });
</script>